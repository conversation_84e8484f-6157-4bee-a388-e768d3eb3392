"use client"

import {
  IconCloseOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { FormEvent, useEffect, useMemo } from "react"

import useExpandedSearch from "@app/hooks/useExpandedSearch"
import InputCloseIcon from "@shared/InputCloseIcon"
import { useMiscStore } from "stores/miscStore"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"

interface ExpandedSearchUnifiedProps {
  variant?: "overlay" | "fullscreen"
}

const ExpandedSearchUnified = ({
  variant = "overlay",
}: ExpandedSearchUnifiedProps) => {
  const {
    showExpandedSearch,
    showSearchResultPreview,
    setShowSearchResultPreview,
    handleClose,
    handleKeyDown,
  } = useExpandedSearch()
  const { setSearchKeyword, searchKeyword } = useMiscStore()

  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )

  const handleInputChange = (e: FormEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    setSearchKeyword(value)
  }

  useEffect(() => {
    setShowSearchResultPreview(searchKeyword.length > 3)
  }, [searchKeyword, setShowSearchResultPreview])

  // Overlay variant styles
  const overlayRootStyles = useMemo(() => {
    return cx(
      "absolute inset-0 z-20 min-h-screen",
      "transition-all duration-500 ease-in-out",
      showExpandedSearch
        ? "pointer-events-auto translate-y-[65px] opacity-100"
        : "pointer-events-none -translate-y-full opacity-0",
    )
  }, [showExpandedSearch])

  const overlayBackdropStyles = useMemo(() => {
    return cx(
      "size-full h-[50vh] bg-black-dim-40",
      "transition-all duration-300",
      showExpandedSearch ? "opacity-100" : "opacity-0",
    )
  }, [showExpandedSearch])

  // Fullscreen variant styles
  const fullscreenRootStyles = useMemo(() => {
    return cx(
      "absolute inset-0 z-40",
      "overflow-y-auto bg-white md:hidden",
      "transition-all duration-[0.3s] ease-in-out",
      showExpandedSearch && "translate-y-0 opacity-100",
      !showExpandedSearch && "-translate-y-full overflow-hidden opacity-0",
    )
  }, [showExpandedSearch])

  if (variant === "fullscreen") {
    return (
      <div className={fullscreenRootStyles}>
        <div className="sticky top-0 flex items-center gap-sm bg-white p-base">
          <Input
            leftIcon={<IconSearchOutline />}
            rightIcon={
              <InputCloseIcon
                text={searchKeyword}
                onClick={() => setSearchKeyword("")}
              />
            }
            size="sm"
            containerInputProps={{
              className: "!bg-gray-w-95 !border-none",
            }}
            onChange={handleInputChange}
            value={searchKeyword}
          />
          <IconCloseOutline width={24} height={24} onClick={handleClose} />
        </div>
        {renderContent}
      </div>
    )
  }

  // Overlay variant (default)
  return (
    <div className={overlayRootStyles}>
      {renderContent}
      <div
        className={overlayBackdropStyles}
        onClick={handleClose}
        onFocus={handleClose}
        onKeyDown={handleKeyDown}
        aria-label="Close expanded search"
      />
    </div>
  )
}

export default ExpandedSearchUnified
