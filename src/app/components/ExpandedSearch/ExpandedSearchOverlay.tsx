"use client"

import { cx } from "class-variance-authority"
import { useMemo } from "react"

import useExpandedSearch from "@app/hooks/useExpandedSearch"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"

const ExpandedSearchOverlay = () => {
  const {
    showExpandedSearch,
    showSearchResultPreview,
    handleClose,
    handleKeyDown,
  } = useExpandedSearch()
  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )

  const animationRoot = useMemo(() => {
    return cx(
      "absolute inset-0 z-20 min-h-screen",
      "transition-all duration-500 ease-in-out",
      showExpandedSearch
        ? "pointer-events-auto translate-y-[65px] opacity-100"
        : "pointer-events-none -translate-y-full opacity-0",
    )
  }, [showExpandedSearch])

  const animationOverlay = useMemo(() => {
    return cx(
      "size-full h-[50vh] bg-black-dim-40",
      "transition-all duration-300",
      showExpandedSearch ? "opacity-100" : "opacity-0",
    )
  }, [showExpandedSearch])

  return (
    <div className={animationRoot}>
      {renderContent}
      <div
        className={animationOverlay}
        onClick={handleClose}
        onFocus={handleClose}
        onKeyDown={handleKeyDown}
        aria-label="Close expanded search"
      />
    </div>
  )
}

export default ExpandedSearchOverlay
