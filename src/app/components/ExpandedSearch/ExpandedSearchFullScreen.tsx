"use client"

import {
  IconCloseOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { FormEvent, useEffect } from "react"

import useExpandedSearch from "@app/hooks/useExpandedSearch"
import InputCloseIcon from "@shared/InputCloseIcon"
import { useMiscStore } from "stores/miscStore"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"

const ExpandedSearchFullScreen = () => {
  const {
    showExpandedSearch,
    showSearchResultPreview,
    setShowSearchResultPreview,
    handleClose,
  } = useExpandedSearch()
  const { setSearchKeyword, searchKeyword } = useMiscStore()

  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )

  const handleInputChange = (e: FormEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    setSearchKeyword(value)
  }

  useEffect(() => {
    setShowSearchResultPreview(searchKeyword.length > 3)
  }, [searchKeyword, setShowSearchResultPreview])

  return (
    <div
      className={cx(
        "absolute inset-0 z-40",
        "overflow-y-auto bg-white md:hidden",
        "transition-all duration-[0.3s] ease-in-out",
        showExpandedSearch && "translate-y-0 opacity-100",
        !showExpandedSearch && "-translate-y-full overflow-hidden opacity-0",
      )}
    >
      <div className="sticky top-0 flex items-center gap-sm bg-white p-base">
        <Input
          leftIcon={<IconSearchOutline />}
          rightIcon={
            <InputCloseIcon
              text={searchKeyword}
              onClick={() => setSearchKeyword("")}
            />
          }
          size="sm"
          containerInputProps={{
            className: "!bg-gray-w-95 !border-none",
          }}
          onChange={handleInputChange}
          value={searchKeyword}
        />
        <IconCloseOutline width={24} height={24} onClick={handleClose} />
      </div>
      {renderContent}
    </div>
  )
}

export default ExpandedSearchFullScreen
